export type FetchOptions<T = any> = {
  endpoint: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
};

function buildUrlWithParams(url: string, params?: Record<string, any>): string {
  if (!params) {
    return url;
  }
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

export async function fetcher<T = any>({
  endpoint,
  params,
  headers = {},
  method = 'GET',
  body = null,
}: FetchOptions): Promise<{data: T | null; error: any; meta: any}> {
  const basesUrlApi = process.env.API_URL;
  const prefixApi = process.env.PREFIX_API;
  const endpointApi = `${basesUrlApi}/${prefixApi}/${endpoint}`;

  const url = buildUrlWithParams(endpointApi, params);

  try {
    let normalizedHeaders: HeadersInit = {};

    if (!( body instanceof FormData )) {
      normalizedHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...headers
      };
    } else {
      normalizedHeaders = headers;
    }

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      mode: 'cors',
      credentials: 'include',
    });

    if (!res.ok) {
      return { data: null, error: await res.json(), meta: null };
    }

    const json = await res.json();
    return { data: json?.data ?? json, meta: json?.meta ?? null, error: false };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
