/* .nc-MainNav {
  &.notOnTop {
    @apply bg-white dark:bg-neutral-900 lg:backdrop-blur-2xl lg:bg-opacity-70  shadow-sm dark:border-b dark:border-neutral-700;
  }
} */

.nc-MainNav1 {
  &.notOnTop {
    @apply bg-white dark:bg-neutral-900 backdrop-blur-2xl bg-opacity-70 dark:bg-opacity-60 shadow-sm dark:border-b dark:border-neutral-700;
  }
}

.menu-item.menu-megamenu:hover {
  > .sub-menu {
    @apply visible;
  }
}

.nc-NavigationAdmin-container {
  // Prevent horizontal scrollbar by constraining to viewport
  max-width: 100vw !important;
  width: 100% !important;
  box-sizing: border-box !important;

  &.collapsed {
    .nc-Navigation-admin {
      display: none;
    }
  }

  &:not(.collapsed) {
    overflow: visible;

    .nc-Navigation-admin {
      z-index: 50;
      max-width: 100vw !important;
      box-sizing: border-box !important;

      .menu-item {
        position: relative;

        &.menu-dropdown:hover {
          > .sub-menu {
            @apply visible;
          }
        }

        .sub-menu {
          z-index: 999999 !important;
          position: relative;
        }

        &.menu-dropdown:hover {
          .sub-menu {
            opacity: 1 !important;
            visibility: visible !important;
          }
        }

        .sub-menu ul {
          background-color: white !important;
          opacity: 1 !important;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .dark & .sub-menu ul {
          background-color: rgb(23, 23, 23) !important;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sub-menu a {
          color: rgb(64, 64, 64) !important;
          opacity: 1 !important;
        }

        .dark & .sub-menu a {
          color: rgb(229, 231, 235) !important;
        }
      }
    }
  }

  .sticky-show-button {
    z-index: 999999;

    animation: slideInFromRight 0.3s ease-out;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideDownFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(10px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUpToTop {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  40% {
    transform: translateY(-10px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.toolbar-slide-down {
  animation: slideDownFromTop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  max-width: 100vw !important;
  box-sizing: border-box !important;

  > div:first-child {
    animation: fadeInScale 0.5s ease-out 0.2s both;

    button {
      animation: buttonGlow 0.8s ease-out 0.3s both;
    }
  }

  > div:last-child {
    animation: fadeInUp 0.5s ease-out 0.3s both;

    .absolute {
      animation: shimmer 1s ease-out 0.5s both;
    }
  }

  .nc-Navigation-admin li {
    animation: fadeInUp 0.4s ease-out both;

    &:nth-child(1) { animation-delay: 0.4s; }
    &:nth-child(2) { animation-delay: 0.5s; }
    &:nth-child(3) { animation-delay: 0.6s; }
    &:nth-child(4) { animation-delay: 0.7s; }
    &:nth-child(5) { animation-delay: 0.8s; }

    &:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease-out;
    }

    .sub-menu {
      animation: none !important;
      transform: none !important;
      opacity: 1 !important;
    }
  }

  @media (max-width: 768px) {
    animation-duration: 0.4s;

    .nc-Navigation-admin li {
      animation-duration: 0.3s;

      &:nth-child(1) { animation-delay: 0.3s; }
      &:nth-child(2) { animation-delay: 0.35s; }
      &:nth-child(3) { animation-delay: 0.4s; }
      &:nth-child(4) { animation-delay: 0.45s; }
      &:nth-child(5) { animation-delay: 0.5s; }
    }
  }

  @media (prefers-reduced-motion: reduce) {
    animation: fadeIn 0.3s ease-out;

    > div:first-child,
    > div:last-child,
    .nc-Navigation-admin li {
      animation: fadeIn 0.2s ease-out;
      animation-delay: 0s !important;
    }
  }
}

.toolbar-slide-up {
  animation: slideUpToTop 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53);

  // Prevent horizontal overflow during animation
  max-width: 100vw !important;
  box-sizing: border-box !important;

  .nc-Navigation-admin li {
    animation: fadeOutDown 0.3s ease-in both;

    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.05s; }
    &:nth-child(3) { animation-delay: 0.1s; }
    &:nth-child(4) { animation-delay: 0.15s; }
    &:nth-child(5) { animation-delay: 0.2s; }
  }

  > div:first-child {
    animation: fadeOutScale 0.3s ease-in 0.1s both;
  }

  > div:last-child {
    animation: fadeOutDown 0.4s ease-in 0.05s both;
  }

  @media (max-width: 768px) {
    animation-duration: 0.3s;

    .nc-Navigation-admin li {
      animation-duration: 0.2s;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.03s; }
      &:nth-child(3) { animation-delay: 0.06s; }
      &:nth-child(4) { animation-delay: 0.09s; }
      &:nth-child(5) { animation-delay: 0.12s; }
    }
  }

  @media (prefers-reduced-motion: reduce) {
    animation: fadeOut 0.2s ease-out;

    > div:first-child,
    > div:last-child,
    .nc-Navigation-admin li {
      animation: fadeOut 0.1s ease-out;
      animation-delay: 0s !important;
    }
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes buttonGlow {
  0% {
    box-shadow: 0 0 0 rgba(40, 129, 67, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(40, 129, 67, 0.3);
  }
  100% {
    box-shadow: 0 0 0 rgba(40, 129, 67, 0);
  }
}

@keyframes shimmer {
  0% {
    opacity: 0;
    transform: translateX(-100%) scaleX(0);
  }
  50% {
    opacity: 1;
    transform: translateX(0) scaleX(1.2);
  }
  100% {
    opacity: 0.2;
    transform: translateX(0) scaleX(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(20px);
    opacity: 0;
  }
}

@keyframes fadeOutScale {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

.nc-NavigationAdmin-container {
  .menu-item.menu-dropdown {
    z-index: auto;

    .sub-menu {
      z-index: 999999 !important;

      position: absolute !important;

      opacity: 0;
      transform: translateY(10px);
      transition: all 0.2s ease-out;
      visibility: hidden;

      ul {
        background: white !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        opacity: 1 !important;

        .dark & {
          background: rgb(23, 23, 23) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
        }

        li {
          opacity: 1 !important;

          a {
            color: rgb(64, 64, 64) !important;
            opacity: 1 !important;
            transition: background-color 0.15s ease;

            .dark & {
              color: rgb(229, 231, 235) !important;
            }

            &:hover {
              background-color: rgba(0, 0, 0, 0.05) !important;

              .dark & {
                background-color: rgba(255, 255, 255, 0.05) !important;
              }
            }
          }
        }
      }
    }

    &:hover .sub-menu {
      visibility: visible !important;
      opacity: 1 !important;
      transform: translateY(0) !important;
    }

    &:not(:hover) .sub-menu {
      opacity: 0 !important;
      transform: translateY(10px) !important;
      visibility: hidden !important;
    }
  }
}

.nc-NavigationAdmin-container {
  .menu-item.menu-dropdown {
    [data-headlessui-state] {
      transition: opacity 0.3s ease-out, transform 0.3s ease-out, visibility 0.3s ease-out !important;
    }

    [class*="transition"],
    [class*="ease-out"],
    [class*="ease-in"],
    [class*="duration-150"] {
      transition: opacity 0.3s ease-out, transform 0.3s ease-out !important;
    }

    [class*="opacity-0"] {
      opacity: 0 !important;
      transform: translateY(15px) !important;
    }

    [class*="opacity-100"] {
      opacity: 1 !important;
      transform: translateY(0) !important;
    }

    [class*="translate-y-1"] {
      transform: translateY(15px) !important;
    }

    [class*="translate-y-0"] {
      transform: translateY(0) !important;
    }

    .sub-menu {
      transition: opacity 0.3s ease-out, transform 0.3s ease-out, visibility 0.3s ease-out !important;

      > div {
        transition: opacity 0.3s ease-out, transform 0.3s ease-out !important;
      }

      &[data-headlessui-state~="open"] {
        opacity: 1 !important;
        transform: translateY(0) !important;
        visibility: visible !important;
      }

      &[data-headlessui-state~="closed"] {
        opacity: 0 !important;
        transform: translateY(15px) !important;
        visibility: hidden !important;
      }
    }
  }
}

.nc-NavigationAdmin-container .menu-item.menu-dropdown {
  .sub-menu {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.25s ease-out, transform 0.25s ease-out, visibility 0.25s ease-out;
    visibility: hidden;
    pointer-events: auto;

    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 200px !important;
    max-width: 280px !important;
    width: max-content !important;

    ul {
      background: white !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
      border-radius: 12px !important;
      z-index: 999999 !important;
      pointer-events: auto !important;
      width: 100% !important;
      max-width: none !important;

      .dark & {
        background: rgb(23, 23, 23) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
      }

      li {
        opacity: 0;
        transform: translateY(5px);
        transition: opacity 0.2s ease-out, transform 0.2s ease-out;
        pointer-events: auto !important;

        a {
          color: rgb(64, 64, 64) !important;
          transition: background-color 0.15s ease !important;
          pointer-events: auto !important;
          z-index: 999999 !important;
          position: relative;
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;

          .dark & {
            color: rgb(229, 231, 235) !important;
          }

          &:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;

            .dark & {
              background-color: rgba(255, 255, 255, 0.05) !important;
            }
          }
        }
      }
    }
  }

  &:nth-last-child(-n+2) .sub-menu,
  &.menu-right .sub-menu {
    left: auto !important;
    right: 0 !important;
  }

  &:hover .sub-menu {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
    pointer-events: auto !important;

    ul li {
      opacity: 1 !important;
      transform: translateY(0) !important;

      &:nth-child(1) { transition-delay: 0.05s; }
      &:nth-child(2) { transition-delay: 0.1s; }
      &:nth-child(3) { transition-delay: 0.15s; }
      &:nth-child(4) { transition-delay: 0.2s; }
      &:nth-child(5) { transition-delay: 0.25s; }
      &:nth-child(6) { transition-delay: 0.3s; }
    }
  }

  &:not(:hover) .sub-menu {
    opacity: 0 !important;
    transform: translateY(10px) !important;
    visibility: hidden !important;
    pointer-events: auto !important;
    transition-delay: 0ms;

    ul li {
      opacity: 0 !important;
      transform: translateY(5px) !important;
      transition-delay: 0ms;
    }
  }
}

@keyframes submenuFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuItemFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes submenuFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(10px);
  }
}

@keyframes menuItemFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(5px);
  }
}

@media (min-width: 769px) {
  .nc-NavigationAdmin-container {
    .menu-item.menu-dropdown {
      .sub-menu {
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 1px;
          height: 1px;
          pointer-events: none;
          visibility: hidden;
        }
      }

      @media (min-width: 1024px) {
        &:nth-last-child(-n+3) .sub-menu {
          left: auto !important;
          right: 0 !important;
        }
      }

      @media (min-width: 1280px) {
        &:nth-last-child(-n+2) .sub-menu {
          left: auto !important;
          right: 0 !important;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .nc-NavigationAdmin-container .menu-item.menu-dropdown {
    &:hover .sub-menu {
      opacity: 1 !important;
      transform: translateY(0) !important;
      visibility: visible !important;
      pointer-events: auto !important;
    }

    .sub-menu {
      pointer-events: auto !important;
      z-index: 999999 !important;
      left: 0 !important;
      right: 0 !important;
      max-width: calc(100vw - 2rem) !important;
      margin: 0 1rem !important;
    }
  }
}

.nc-Navigation {
  position: relative;
  z-index: 100;

  .menu-item {
    position: relative;

    .sub-menu {
      z-index: 999999 !important;
    }
  }
}

.AvatarDropdown {
  position: relative;
  z-index: 1;
}

.nc-NavigationAdmin-container .menu-item.menu-dropdown {
  .sub-menu {
    container-type: inline-size;

    ul {
      li a {
        max-width: 250px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
      }
    }
  }

  &:nth-last-child(1) .sub-menu,
  &:nth-last-child(2) .sub-menu {
    left: auto !important;
    right: 0 !important;

    ul {
      transform-origin: top right !important;
    }
  }
}

@supports not (container-type: inline-size) {
  .nc-NavigationAdmin-container .menu-item.menu-dropdown {
    .sub-menu {
      max-width: min(280px, calc(100vw - 2rem)) !important;

      ul {
        max-width: 100% !important;
        overflow: hidden !important;
      }
    }

    &:nth-last-child(-n+2) .sub-menu {
      left: auto !important;
      right: 0 !important;
    }
  }
}

.nc-NavigationAdmin-container .menu-item.menu-dropdown .sub-menu {
  max-width: calc(100vw - 2rem) !important;

  width: clamp(200px, 280px, calc(100vw - 2rem)) !important;

  ul {
    width: 100% !important;
    max-width: none !important;

    li a {
      max-width: 100% !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      display: block !important;
    }
  }
}

@media (min-width: 640px) {
  .nc-NavigationAdmin-container {
    .menu-item.menu-dropdown {
      &:nth-last-child(1) .sub-menu,
      &:nth-last-child(2) .sub-menu {
        left: auto !important;
        right: 0 !important;
        transform-origin: top right !important;
      }
    }
  }
}

@media (min-width: 1024px) {
  .nc-NavigationAdmin-container {
    .menu-item.menu-dropdown {
      &:nth-last-child(1) .sub-menu {
        left: auto !important;
        right: 0 !important;
      }
    }
  }
}

.nc-NavigationAdmin-container .menu-item.menu-dropdown .sub-menu {
  position: absolute !important;
  top: 100% !important;
  z-index: 999999 !important;

  overflow: visible !important;

  ul {
    overflow: hidden !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  }
}


